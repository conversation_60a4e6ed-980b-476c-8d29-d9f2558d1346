import React from "react";
import { Button } from "@/components/ui/button";
import { Section } from "@prisma/client";
import { Link } from "lucide-react";
import { toast } from "sonner";
import {
  createExternalSectionLinkAction,
  createExternalTextLinkAction,
} from "@/actions/external-link";

type Props = {
  section: Section & { text?: Text & { externalLink: null } };
};

const ExternalLinks = ({ section }: Props) => {
  const handleExternalLink = async () => {
    const { data, message, error } = await createExternalTextLinkAction({
      textId: section?.text.id,
      label: "",
      url: "",
    });

    if (error) {
      return toast.error(error.message);
    }

    if (!data) {
      return toast.error(message);
    }

    toast.success(message);
  };

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted"
        title="External Link section"
      >
        <Link className="h-4 w-4" />
      </Button>
    </>
  );
};

export default ExternalLinks;
